"""
向量化处理器
专门处理BGE-M3向量化任务，基于原向量化服务的实现
支持环境感知：自动适配Celery异步环境和同步测试环境
"""

import gc
import os
import sys
import time
import asyncio
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain.schema import Document
from langchain_chroma import Chroma
import chromadb

from shared.utils.logger import get_logger
from services.worker_service.config import config
from services.worker_service.core.embedding_client import SyncEmbeddingServiceClient
from shared.models.file_info import FileInfo, FileStatus

logger = get_logger(__name__)


class VectorizationProcessor:
    """向量化处理器 - 支持环境感知"""

    def __init__(self):
        self.embedding_service = None
        self.chroma_client = chromadb.HttpClient(
            host=config.chroma_host, port=config.chroma_port
        )
        self.chunk_size = config.chunk_size
        self.chunk_overlap = config.chunk_overlap
        self.max_chunks = config.max_chunks_per_file

        # 环境检测
        self._is_celery_env = self._detect_celery_environment()
        self._is_sync_test_env = self._detect_sync_test_environment()

        logger.info("向量化处理器初始化完成")
        env_type = 'Celery' if self._is_celery_env else 'Sync Test' if self._is_sync_test_env else 'Unknown'
        logger.info(f"  运行环境: {env_type}")
        logger.info(f"  ChromaDB URL: {config.chroma_url}")
        logger.info(f"  ChromaDB API版本: {config.chroma_api_version}")
        logger.info(f"  集合前缀: {config.chroma_collection_prefix}")

    def _detect_celery_environment(self) -> bool:
        """检测是否在Celery worker环境中运行"""
        try:
            # 检查Celery相关的环境变量
            env_indicators = [
                'CELERY_WORKER_RUNNING' in os.environ,
                'celery' in os.environ.get('_', '').lower(),
                'CELERY_CURRENT_TASK' in os.environ,
            ]

            # 检查是否有Celery相关的进程名
            celery_in_argv = any('celery' in arg.lower() for arg in sys.argv)

            # 检查是否在Celery任务上下文中
            try:
                from celery import current_task
                in_task_context = current_task is not None
            except (ImportError, RuntimeError):
                in_task_context = False

            # 检查进程名
            try:
                import psutil
                current_process = psutil.Process()
                process_name = current_process.name().lower()
                celery_in_process = 'celery' in process_name
            except (ImportError, Exception):
                celery_in_process = False

            is_celery = any(env_indicators) or celery_in_argv or in_task_context or celery_in_process
            logger.debug(f"Celery环境检测结果: {is_celery} (env: {any(env_indicators)}, argv: {celery_in_argv}, task: {in_task_context}, process: {celery_in_process})")
            return is_celery

        except Exception as e:
            logger.debug(f"Celery环境检测失败: {e}")
            return False

    def _detect_sync_test_environment(self) -> bool:
        """检测是否在同步测试环境中运行"""
        try:
            # 检查测试相关的环境变量和模块
            test_indicators = [
                'test' in os.environ.get('PYTEST_CURRENT_TEST', '').lower(),
                'test' in __file__.lower(),
                any('test' in arg.lower() for arg in sys.argv),
            ]

            is_test = any(test_indicators)
            logger.debug(f"同步测试环境检测结果: {is_test}")
            return is_test

        except Exception as e:
            logger.debug(f"同步测试环境检测失败: {e}")
            return False

    def _get_embedding_service(self):
        """延迟初始化嵌入服务客户端 - 环境感知"""
        if self.embedding_service is None:
            self.embedding_service = SyncEmbeddingServiceClient(
                base_url=config.embedding_service_url,
                timeout=config.embedding_service_timeout,
                max_retries=config.embedding_service_max_retries,
            )

            # 根据环境决定是否执行健康检查
            if self._is_sync_test_env:
                logger.info("嵌入服务客户端创建成功（同步测试环境，跳过健康检查）")
            elif self._is_celery_env:
                logger.info("嵌入服务客户端创建成功（Celery环境，支持健康检查）")
            else:
                logger.info("嵌入服务客户端创建成功（默认环境）")

        return self.embedding_service

    def health_check(self) -> Dict[str, Any]:
        """
        环境感知的健康检查
        根据运行环境选择合适的检查策略
        """
        if self._is_sync_test_env:
            return self._sync_test_health_check()
        elif self._is_celery_env:
            return self._celery_health_check()
        else:
            return self._default_health_check()

    def _sync_test_health_check(self) -> Dict[str, Any]:
        """同步测试环境的健康检查（简化版）"""
        logger.info("执行同步测试环境健康检查（跳过异步操作）")

        try:
            # 只检查基本的服务初始化
            embedding_service = self._get_embedding_service()
            embedding_healthy = embedding_service is not None

            # 简单的ChromaDB连接检查（不执行实际操作）
            chroma_healthy = self.chroma_client is not None

            return {
                'healthy': embedding_healthy and chroma_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'sync_test',
                'note': '跳过异步操作以避免事件循环冲突'
            }

        except Exception as e:
            logger.error(f"同步测试环境健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'sync_test'
            }

    def _celery_health_check(self) -> Dict[str, Any]:
        """Celery环境的完整健康检查"""
        logger.info("执行Celery环境完整健康检查")

        try:
            # 检查嵌入服务
            embedding_service = self._get_embedding_service()
            embedding_healthy = False

            if embedding_service:
                try:
                    # 在Celery环境中可以安全地调用健康检查
                    health_result = embedding_service.health_check()
                    # 处理不同类型的健康检查返回值
                    if isinstance(health_result, dict):
                        embedding_healthy = health_result.get('healthy', False)
                    elif isinstance(health_result, bool):
                        embedding_healthy = health_result
                    else:
                        embedding_healthy = False
                except Exception as e:
                    logger.warning(f"嵌入服务健康检查失败: {e}")
                    embedding_healthy = False

            # 检查ChromaDB连接
            chroma_healthy = False
            try:
                version_info = self.chroma_client.get_version()
                chroma_healthy = version_info is not None
                logger.info(f"ChromaDB版本: {version_info}")
            except Exception as e:
                logger.error(f"ChromaDB连接检查失败: {e}")
                chroma_healthy = False

            overall_healthy = embedding_healthy and chroma_healthy

            return {
                'healthy': overall_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'celery',
                'note': '完整健康检查已执行'
            }

        except Exception as e:
            logger.error(f"Celery环境健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'celery'
            }

    def _default_health_check(self) -> Dict[str, Any]:
        """默认健康检查"""
        logger.info("执行默认健康检查")

        try:
            # 基本检查
            embedding_service = self._get_embedding_service()
            embedding_healthy = embedding_service is not None
            chroma_healthy = self.chroma_client is not None

            return {
                'healthy': embedding_healthy and chroma_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'default',
                'note': '基本健康检查'
            }

        except Exception as e:
            logger.error(f"默认健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'default'
            }

    def _get_collection_name(self, file_code: str) -> str:
        """获取集合名称"""
        return f"{config.chroma_collection_prefix}{file_code}"

    def _create_chroma_collection(self, collection_name: str) -> bool:
        """创建ChromaDB集合"""
        try:
            # 尝试获取集合，如果不存在则创建
            try:
                collection = self.chroma_client.get_collection(name=collection_name)
                logger.info(f"集合已存在: {collection_name}")
                return True
            except Exception:
                # 集合不存在，创建新集合
                collection = self.chroma_client.create_collection(
                    name=collection_name,
                    metadata={
                        "created_at": datetime.now().isoformat(),
                        "service": "worker-service",
                        "version": config.service_version,
                    },
                )
                logger.info(f"集合创建成功: {collection_name}")
                return True

        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            return False

    def process_csv_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理CSV文件 - 基于原有CSV处理逻辑
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding="utf-8")
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用原有的分块逻辑创建文档
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ CSV文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise

    def process_excel_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理Excel文件 - 基于原有逻辑
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用相同的分块逻辑
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ Excel文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            raise

    def _create_chunked_documents_from_df(
        self, df: pd.DataFrame, file_path: str, file_code: str
    ) -> List[Document]:
        """
        从DataFrame创建分块文档 - 基于原有实现
        """
        documents = []
        total_rows = len(df)

        # 计算每个块的行数
        rows_per_chunk = max(1, self.chunk_size // 100)  # 简单估算

        for start_idx in range(0, total_rows, rows_per_chunk):
            end_idx = min(start_idx + rows_per_chunk, total_rows)
            chunk_df = df.iloc[start_idx:end_idx]

            # 生成块内容
            chunk_content = self._format_chunk_content(chunk_df, start_idx)

            # 创建文档
            doc = Document(
                page_content=chunk_content,
                metadata={
                    "file_code": file_code,
                    "file_path": file_path,
                    "chunk_index": len(documents),
                    "start_row": start_idx,
                    "end_row": end_idx - 1,
                    "total_rows": len(chunk_df),
                    "created_at": datetime.now().isoformat(),
                },
            )

            documents.append(doc)

            # 限制最大块数
            if len(documents) >= self.max_chunks:
                logger.warning(f"达到最大块数限制 {self.max_chunks}，停止处理")
                break

        return documents

    def _format_chunk_content(self, chunk_df: pd.DataFrame, start_row: int) -> str:
        """
        格式化块内容 - 基于原有实现
        """
        content_parts = []

        # 添加表头信息
        content_parts.append("=== 数据表信息 ===")
        content_parts.append(f"列名: {', '.join(chunk_df.columns)}")
        content_parts.append(f"数据行数: {len(chunk_df)}")
        content_parts.append(f"起始行号: {start_row + 1}")
        content_parts.append("")

        # 添加数据行
        for idx, (_, row) in enumerate(chunk_df.iterrows()):
            content_parts.append(f"\n---记录 {start_row + idx + 1}---")
            for col in chunk_df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                content_parts.append(f"{col}: {value}")

        return "\n".join(content_parts)

    def store_documents(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        存储文档到ChromaDB - 基于原有实现
        """
        try:
            collection_name = self._get_collection_name(file_code)

            # 创建集合
            collection_created = self._create_chroma_collection(collection_name)
            if not collection_created:
                return False

            # 获取嵌入服务
            embedding_service = self._get_embedding_service()

            # 生成嵌入向量
            logger.info(f"开始生成嵌入向量: {len(documents)} 个文档")
            start_time = time.time()

            texts = [doc.page_content for doc in documents]
            embeddings = embedding_service.embed_documents(texts)

            embedding_time = time.time() - start_time
            logger.info(f"嵌入向量生成完成，耗时 {embedding_time:.2f}s")

            # 准备存储数据
            ids = [f"{file_code}_{i}" for i in range(len(documents))]
            metadatas = [doc.metadata for doc in documents]

            # 存储到ChromaDB
            collection = self.chroma_client.get_collection(name=collection_name)
            collection.add(
                ids=ids, embeddings=embeddings, documents=texts, metadatas=metadatas
            )

            logger.info(f"文档存储成功: {file_code}, {len(documents)} 个文档")
            return True

        except Exception as e:
            logger.error(f"存储文档失败: {e}")
            return False
        finally:
            # 清理嵌入服务客户端
            if self.embedding_service:
                try:
                    self.embedding_service.close()
                except Exception as e:
                    logger.warning(f"清理嵌入服务客户端时出错: {e}")

    def process_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理文件（自动识别类型）
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()

        if file_ext == ".csv":
            return self.process_csv_file(str(file_path), file_code)
        elif file_ext in [".xlsx", ".xls"]:
            return self.process_excel_file(str(file_path), file_code)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

    def cleanup(self):
        """环境感知的资源清理"""
        logger.info(f"开始清理向量化处理器资源 (环境: {self._get_environment_name()})")

        try:
            if self._is_celery_env:
                self._celery_cleanup()
            elif self._is_sync_test_env:
                self._sync_test_cleanup()
            else:
                self._default_cleanup()

            logger.info("向量化处理器资源清理完成")

        except Exception as e:
            logger.error(f"向量化处理器资源清理失败: {e}")

    def _get_environment_name(self) -> str:
        """获取环境名称"""
        if self._is_celery_env:
            return "Celery"
        elif self._is_sync_test_env:
            return "Sync Test"
        else:
            return "Default"

    def _celery_cleanup(self):
        """Celery环境的资源清理"""
        logger.debug("执行Celery环境资源清理")

        # 清理嵌入服务
        if self.embedding_service:
            try:
                # 在Celery环境中可以安全地调用close方法
                if hasattr(self.embedding_service, 'close'):
                    self.embedding_service.close()
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Celery)")
            except Exception as e:
                logger.warning(f"Celery环境清理嵌入服务时出错: {e}")
                self.embedding_service = None

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 强制垃圾回收
        self._force_garbage_collection()

    def _sync_test_cleanup(self):
        """同步测试环境的资源清理"""
        logger.debug("执行同步测试环境资源清理")

        # 简化清理，避免事件循环问题
        if self.embedding_service:
            try:
                # 直接设置为None，避免调用可能的异步方法
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Sync Test)")
            except Exception as e:
                logger.warning(f"同步测试环境清理嵌入服务时出错: {e}")

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 轻量级垃圾回收
        gc.collect()

    def _default_cleanup(self):
        """默认环境的资源清理"""
        logger.debug("执行默认环境资源清理")

        # 清理嵌入服务
        if self.embedding_service:
            try:
                if hasattr(self.embedding_service, 'close'):
                    self.embedding_service.close()
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Default)")
            except Exception as e:
                logger.warning(f"默认环境清理嵌入服务时出错: {e}")
                self.embedding_service = None

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 强制垃圾回收
        self._force_garbage_collection()

    def _cleanup_chroma_client(self):
        """清理ChromaDB客户端"""
        try:
            # ChromaDB HttpClient不需要显式关闭，直接设置为None
            self.chroma_client = None
            logger.debug("ChromaDB客户端已清理")
        except Exception as e:
            logger.warning(f"清理ChromaDB客户端时出错: {e}")

    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            # 多次垃圾回收确保资源释放
            for _ in range(3):
                gc.collect()
            logger.debug("强制垃圾回收完成")
        except Exception as e:
            logger.warning(f"强制垃圾回收时出错: {e}")
