"""
向量化处理器
专门处理BGE-M3向量化任务，基于原向量化服务的实现
"""

import gc
import time
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain.schema import Document
from langchain_chroma import Chroma
import chromadb

from shared.utils.logger import get_logger
from services.worker_service.config import config
from services.worker_service.core.embedding_client import SyncEmbeddingServiceClient
from shared.models.file_info import FileInfo, FileStatus

logger = get_logger(__name__)


class VectorizationProcessor:
    """向量化处理器"""

    def __init__(self):
        self.embedding_service = None
        self.chroma_client = chromadb.HttpClient(
            host=config.chroma_host, port=config.chroma_port
        )
        self.chunk_size = config.chunk_size
        self.chunk_overlap = config.chunk_overlap
        self.max_chunks = config.max_chunks_per_file

        logger.info(f"向量化处理器初始化完成")
        logger.info(f"  ChromaDB URL: {config.chroma_url}")
        logger.info(f"  ChromaDB API版本: {config.chroma_api_version}")
        logger.info(f"  集合前缀: {config.chroma_collection_prefix}")

    def _get_embedding_service(self):
        """延迟初始化嵌入服务客户端"""
        if self.embedding_service is None:
            self.embedding_service = SyncEmbeddingServiceClient(
                base_url=config.embedding_service_url,
                timeout=config.embedding_service_timeout,
                max_retries=config.embedding_service_max_retries,
            )

            # 跳过健康检查（在同步环境中会有事件循环问题）
            logger.info("嵌入服务客户端创建成功（跳过健康检查）")

        return self.embedding_service

    def _get_collection_name(self, file_code: str) -> str:
        """获取集合名称"""
        return f"{config.chroma_collection_prefix}{file_code}"

    def _create_chroma_collection(self, collection_name: str) -> bool:
        """创建ChromaDB集合"""
        try:
            # 尝试获取集合，如果不存在则创建
            try:
                collection = self.chroma_client.get_collection(name=collection_name)
                logger.info(f"集合已存在: {collection_name}")
                return True
            except Exception:
                # 集合不存在，创建新集合
                collection = self.chroma_client.create_collection(
                    name=collection_name,
                    metadata={
                        "created_at": datetime.now().isoformat(),
                        "service": "worker-service",
                        "version": config.service_version,
                    },
                )
                logger.info(f"集合创建成功: {collection_name}")
                return True

        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            return False

    def process_csv_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理CSV文件 - 基于原有CSV处理逻辑
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding="utf-8")
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用原有的分块逻辑创建文档
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ CSV文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise

    def process_excel_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理Excel文件 - 基于原有逻辑
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用相同的分块逻辑
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ Excel文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            raise

    def _create_chunked_documents_from_df(
        self, df: pd.DataFrame, file_path: str, file_code: str
    ) -> List[Document]:
        """
        从DataFrame创建分块文档 - 基于原有实现
        """
        documents = []
        total_rows = len(df)

        # 计算每个块的行数
        rows_per_chunk = max(1, self.chunk_size // 100)  # 简单估算

        for start_idx in range(0, total_rows, rows_per_chunk):
            end_idx = min(start_idx + rows_per_chunk, total_rows)
            chunk_df = df.iloc[start_idx:end_idx]

            # 生成块内容
            chunk_content = self._format_chunk_content(chunk_df, start_idx)

            # 创建文档
            doc = Document(
                page_content=chunk_content,
                metadata={
                    "file_code": file_code,
                    "file_path": file_path,
                    "chunk_index": len(documents),
                    "start_row": start_idx,
                    "end_row": end_idx - 1,
                    "total_rows": len(chunk_df),
                    "created_at": datetime.now().isoformat(),
                },
            )

            documents.append(doc)

            # 限制最大块数
            if len(documents) >= self.max_chunks:
                logger.warning(f"达到最大块数限制 {self.max_chunks}，停止处理")
                break

        return documents

    def _format_chunk_content(self, chunk_df: pd.DataFrame, start_row: int) -> str:
        """
        格式化块内容 - 基于原有实现
        """
        content_parts = []

        # 添加表头信息
        content_parts.append("=== 数据表信息 ===")
        content_parts.append(f"列名: {', '.join(chunk_df.columns)}")
        content_parts.append(f"数据行数: {len(chunk_df)}")
        content_parts.append(f"起始行号: {start_row + 1}")
        content_parts.append("")

        # 添加数据行
        for idx, (_, row) in enumerate(chunk_df.iterrows()):
            content_parts.append(f"\n---记录 {start_row + idx + 1}---")
            for col in chunk_df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                content_parts.append(f"{col}: {value}")

        return "\n".join(content_parts)

    def store_documents(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        存储文档到ChromaDB - 基于原有实现
        """
        try:
            collection_name = self._get_collection_name(file_code)

            # 创建集合
            collection_created = self._create_chroma_collection(collection_name)
            if not collection_created:
                return False

            # 获取嵌入服务
            embedding_service = self._get_embedding_service()

            # 生成嵌入向量
            logger.info(f"开始生成嵌入向量: {len(documents)} 个文档")
            start_time = time.time()

            texts = [doc.page_content for doc in documents]
            embeddings = embedding_service.embed_documents(texts)

            embedding_time = time.time() - start_time
            logger.info(f"嵌入向量生成完成，耗时 {embedding_time:.2f}s")

            # 准备存储数据
            ids = [f"{file_code}_{i}" for i in range(len(documents))]
            metadatas = [doc.metadata for doc in documents]

            # 存储到ChromaDB
            collection = self.chroma_client.get_collection(name=collection_name)
            collection.add(
                ids=ids, embeddings=embeddings, documents=texts, metadatas=metadatas
            )

            logger.info(f"文档存储成功: {file_code}, {len(documents)} 个文档")
            return True

        except Exception as e:
            logger.error(f"存储文档失败: {e}")
            return False
        finally:
            # 清理嵌入服务客户端
            if self.embedding_service:
                try:
                    self.embedding_service.close()
                except Exception as e:
                    logger.warning(f"清理嵌入服务客户端时出错: {e}")

    def process_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理文件（自动识别类型）
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()

        if file_ext == ".csv":
            return self.process_csv_file(str(file_path), file_code)
        elif file_ext in [".xlsx", ".xls"]:
            return self.process_excel_file(str(file_path), file_code)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

    def cleanup(self):
        """清理资源"""
        if self.embedding_service:
            try:
                # 对于同步客户端，直接设置为None，避免事件循环问题
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理")
            except Exception as e:
                logger.warning(f"清理嵌入服务客户端时出错: {e}")

        # ChromaDB Python客户端不需要显式关闭
        self.chroma_client = None

        # 强制垃圾回收
        gc.collect()
